<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JCS Ping-o-matic Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <div class="tech-header">
            <div class="tech-circles">
                <div class="tech-circle tech-circle-1"></div>
                <div class="tech-circle tech-circle-2"></div>
            </div>
            <h1>JCS Ping-o-matic Tool</h1>
            <p>Generate backlinks by pinging your URLs to backlink generator sites</p>
        </div>

        <div class="card glow-effect mb-4">
            <div class="card-header">
                <i class="fas fa-cogs"></i> Configuration
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="feedUrl" class="form-label">Feed URL:</label>
                        <input type="url" class="form-control" id="feedUrl" value="https://joaocesarsilva.com/atomfeed.xml" required>
                    </div>
                    <div class="col-md-6">
                        <label for="sitemapUrl" class="form-label">Sitemap URL:</label>
                        <input type="url" class="form-control" id="sitemapUrl" value="https://joaocesarsilva.com/sitemap.xml" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="urlLimit" class="form-label">Maximum URLs to process: <span id="urlLimitValue">0 (No Limit)</span></label>
                        <input type="range" class="form-range" id="urlLimit" min="0" max="1000" value="0" oninput="document.getElementById('urlLimitValue').textContent = this.value === '0' ? '0 (No Limit)' : this.value">
                    </div>
                    <div class="col-md-6">
                        <label for="backlinkParallelWorkers" class="form-label">Backlink Parallel Workers: <span id="backlinkParallelWorkersValue">5</span></label>
                        <input type="range" class="form-range" id="backlinkParallelWorkers" min="1" max="30" value="5" oninput="document.getElementById('backlinkParallelWorkersValue').textContent = this.value">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="backlinkGeneratorsPerUrl" class="form-label">Backlink Generators Per URL: <span id="backlinkGeneratorsPerUrlValue">5</span></label>
                        <input type="range" class="form-range" id="backlinkGeneratorsPerUrl" min="1" max="39" value="5" oninput="document.getElementById('backlinkGeneratorsPerUrlValue').textContent = this.value">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12 d-flex align-items-end justify-content-between">
                        <button type="button" class="btn btn-primary flex-grow-1" id="startButton" onclick="startProcess()">
                            <i class="fas fa-play"></i> Start Pinging
                        </button>
                        <button type="button" class="btn btn-danger flex-grow-1 ms-2" id="stopButton" onclick="stopProcess()" disabled>
                            <i class="fas fa-stop"></i> Stop Process
                        </button>
                        <button type="button" class="btn btn-success flex-grow-1 ms-2" id="managePingSitesButton" onclick="showManagePingSitesModal()">
                            <i class="fas fa-server"></i> Manage Ping Sites
                        </button>
                        <button type="button" class="btn btn-info flex-grow-1 ms-2" id="indexNowButton" onclick="runIndexNow()">
                            <i class="fas fa-sync"></i> Run IndexNow
                        </button>
                        <a href="facebook_tags.php" target="_blank" class="btn btn-info flex-grow-1 ms-2" id="facebookDebugLink">
                        <i class="fab fa-facebook"></i> Facebook Debug
                    </a>
                        <button type="button" class="btn btn-warning flex-grow-1 ms-2" id="fetchUrlsButton" onclick="fetchUrls()">
                            <i class="fas fa-download"></i> Fetch URLs
                        </button>
                        <button type="button" class="btn btn-secondary flex-grow-1 ms-2" id="selectUrlsButton" onclick="showSelectUrlsModal()">
                            <i class="fas fa-check-square"></i> Select URLs
                        </button>
                        <button type="button" class="btn btn-success flex-grow-1 ms-2" id="productsButton" onclick="generatePinterestCSV()">
                            <i class="fab fa-pinterest"></i> Download Pinterest CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="stats-label">URLs Processed</div>
                    <div class="stats-value" id="urlsProcessed">0</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="stats-label">URLs Skipped</div>
                    <div class="stats-value" id="urlsSkipped">0</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="stats-label">Successful Pings</div>
                    <div class="stats-value text-success" id="successPings">0</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="stats-label">Failed Pings</div>
                    <div class="stats-value text-danger" id="failedPings">0</div>
                </div>
            </div>
        </div>

        <div class="card glow-effect mb-4">
            <div class="card-header">
                <i class="fas fa-tasks"></i> Progress
            </div>
            <div class="card-body">
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="text-end mt-1" id="progressText">0%</div>
                <div id="currentTask" class="mt-3 text-center">Ready to start</div>
            </div>
        </div>

        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab" aria-controls="activity" aria-selected="true">
                    <i class="fas fa-stream"></i> Activity Log
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results" type="button" role="tab" aria-controls="results" aria-selected="false">
                    <i class="fas fa-check-circle"></i> Results
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="failed-tab" data-bs-toggle="tab" data-bs-target="#failed" type="button" role="tab" aria-controls="failed" aria-selected="false">
                    <i class="fas fa-exclamation-triangle"></i> Failed Links
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="non-responsive-tab" data-bs-toggle="tab" data-bs-target="#non-responsive" type="button" role="tab" aria-controls="non-responsive" aria-selected="false">
                    <i class="fas fa-unlink"></i> Non-Responsive Sites
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="processed-tab" data-bs-toggle="tab" data-bs-target="#processed" type="button" role="tab" aria-controls="processed" aria-selected="false">
                    <i class="fas fa-list"></i> All URLs
                </button>
            </li>
        </ul>
        
        <div class="tab-content" id="mainTabsContent">
            <div class="tab-pane fade show active" id="activity" role="tabpanel" aria-labelledby="activity-tab">
                <div class="card glow-effect">
                    <div class="card-body p-0">
                        <div class="log-container" id="activityLog">
                            <div class="log-entry log-info">System ready. Configure settings and click "Start Pinging" to begin.</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="results" role="tabpanel" aria-labelledby="results-tab">
                <div class="card glow-effect">
                    <div class="card-body p-0">
                        <div id="resultsContainer" class="p-3">
                            <div class="text-center text-muted py-5">Results will appear here as URLs are processed</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="failed" role="tabpanel" aria-labelledby="failed-tab">
                <div class="card glow-effect">
                    <div class="card-body">
                        <div id="failedLinksContainer">
                            <div class="text-center text-muted py-5">Failed links will appear here during processing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="non-responsive" role="tabpanel" aria-labelledby="non-responsive-tab">
                <div class="card glow-effect">
                    <div class="card-body">
                        <div id="nonResponsiveSitesContainer">
                            <div class="text-center text-muted py-5">Non-responsive ping sites will appear here during processing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="processed" role="tabpanel" aria-labelledby="processed-tab">
                <div class="card glow-effect">
                    <div class="card-body">
                        <div id="processedUrlsContainer">
                            <div class="text-center text-muted py-5">Processed URLs will appear here during processing</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Manage Ping Sites Modal -->
    <div class="modal fade" id="managePingSitesModal" tabindex="-1" aria-labelledby="managePingSitesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="managePingSitesModalLabel">Manage Ping Sites</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" onclick="addNewPingSite()">
                            <i class="fas fa-plus"></i> Add New Ping Site
                        </button>
                        <button type="button" class="btn btn-danger ms-2" onclick="removeAllNonResponsiveSites()">
                            <i class="fas fa-trash-alt"></i> Remove All Non-Responsive Sites
                        </button>
                    </div>
                    
                    <div class="ping-sites-list" id="pingSitesList">
                        <!-- Ping sites will be loaded here -->
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading ping sites...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="savePingSitesToServer()">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Ping Site Modal -->
    <div class="modal fade" id="editPingSiteModal" tabindex="-1" aria-labelledby="editPingSiteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPingSiteModalLabel">Add Ping Site</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="pingSiteForm">
                        <input type="hidden" id="pingSiteIndex" value="-1">
                        <div class="mb-3">
                            <label for="pingSiteName" class="form-label">Site Name</label>
                            <input type="text" class="form-control" id="pingSiteName" required>
                        </div>
                        <div class="mb-3">
                            <label for="pingSiteUrl" class="form-label">Ping URL</label>
                            <input type="url" class="form-control" id="pingSiteUrl" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="savePingSite()">Save</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Select URLs Modal -->
    <div class="modal fade" id="selectUrlsModal" tabindex="-1" aria-labelledby="selectUrlsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="selectUrlsModalLabel">Select URLs to Ping</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllUrls()">Select All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllUrls()">Deselect All</button>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="markSelectedUrlsToSkip()">Mark Selected to Skip</button>
                        </div>
                        <div id="urlSelectionList" class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                            <div class="text-center text-muted py-5">
                                Fetch URLs first by clicking "Start Pinging" button
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="applyUrlSelection()">Apply Selection</button>
                </div>
            </div>
        </div>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Utility function to shuffle an array (Fisher-Yates algorithm)
        function shuffleArray(array) {
            const newArray = [...array]; // Create a copy to avoid modifying the original
            for (let i = newArray.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [newArray[i], newArray[j]] = [newArray[j], newArray[i]]; // Swap elements
            }
            return newArray;
        }

        // Global variables
        let isProcessing = false;
        let shouldStop = false;
        let currentUrlIndex = 0;
        let totalUrls = 0;
        let urls = [];
        let nonResponsiveSites = [];
        let failedLinks = [];
        let selectedUrls = []; // Array to store selected URLs
        let allExtractedUrls = []; // Array to store all extracted URLs
        let skippedUrls = []; // Array to store URLs marked to skip
        let pingSites = {};
        let pingSitesModified = false;
        
        let stats = {
            urlsProcessed: 0,
            urlsSkipped: 0,
            successPings: 0,
            failedPings: 0
        };

        // Initialize modals
        // Show manage ping sites modal
        function showManagePingSitesModal() {
            // Load ping sites from server
            loadPingSitesFromServer();
            
            // Show the modal
            managePingSitesModal.show();
        }
        
        // Load ping sites from server
        async function loadPingSitesFromServer() {
            try {
                const response = await fetch('ajax_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_ping_sites'
                    }),
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to load ping sites');
                }
                
                // Store the ping sites
                pingSites = data.sites;
                
                // Update the UI
                renderPingSitesList();
                
                return true;
            } catch (error) {
                addLog(`Error loading ping sites: ${error.message}`, 'error');
                
                // Show error in the ping sites list
                document.getElementById('pingSitesList').innerHTML = `
                    <div class="alert alert-danger">
                        Error loading ping sites: ${error.message}
                        <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="loadPingSitesFromServer()">
                            Try Again
                        </button>
                    </div>
                `;
                
                return false;
            }
        }
        
        // Render ping sites list
        function renderPingSitesList() {
            const pingSitesList = document.getElementById('pingSitesList');
            
            if (!pingSites || Object.keys(pingSites).length === 0) {
                pingSitesList.innerHTML = `
                    <div class="alert alert-info">
                        No ping sites found. Add some using the button above.
                    </div>
                `;
                return;
            }
            
            let html = `
                <div class="alert alert-info mb-3">
                    <h5>Ping Site Configuration Help</h5>
                    <p>Ping sites should be URLs that accept ping requests for your content. They typically require a URL parameter.</p>
                    <p>Most ping services use the format: <code>https://example.com/ping?url=</code> or <code>https://example.com/ping/?url=</code></p>
                    <p>The system will automatically add your URL to this parameter when pinging.</p>
                    <p>Some services may require additional parameters like <code>&title=</code> which are automatically added by the system.</p>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>URL</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            for (const [name, url] of Object.entries(pingSites)) {
                html += `
                    <tr>
                        <td>${name}</td>
                        <td><a href="${url}" target="_blank" class="text-break">${url}</a></td>
                        <td>
                            <button type="button" class="btn btn-sm btn-primary me-1" onclick="editPingSite('${name}', '${url}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger" onclick="removePingSite('${name}')">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            pingSitesList.innerHTML = html;
        }
        
        // Save ping sites to server
        async function savePingSitesToServer() {
            try {
                const response = await fetch('ajax_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save_ping_sites',
                        sites: pingSites
                    }),
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to save ping sites');
                }
                
                addLog('Ping sites saved successfully', 'success');
                
                // Close the modal
                managePingSitesModal.hide();
                
                return true;
            } catch (error) {
                addLog(`Error saving ping sites: ${error.message}`, 'error');
                return false;
            }
        }
        
        // Add new ping site
        function addNewPingSite() {
            // Clear the form
            document.getElementById('pingSiteName').value = '';
            document.getElementById('pingSiteUrl').value = '';
            document.getElementById('editPingSiteModalLabel').textContent = 'Add New Ping Site';
            
            // Show the modal
            editPingSiteModal.show();
        }
        
        // Edit ping site
        function editPingSite(name, url) {
            // Fill the form
            document.getElementById('pingSiteName').value = name;
            document.getElementById('pingSiteUrl').value = url;
            document.getElementById('editPingSiteModalLabel').textContent = 'Edit Ping Site';
            
            // Store the original name for reference
            document.getElementById('pingSiteName').dataset.originalName = name;
            
            // Show the modal
            editPingSiteModal.show();
        }
        
        // Save ping site (from edit modal)
        function savePingSite() {
            const nameInput = document.getElementById('pingSiteName');
            const urlInput = document.getElementById('pingSiteUrl');
            
            const name = nameInput.value.trim();
            const url = urlInput.value.trim();
            const originalName = nameInput.dataset.originalName || '';
            
            if (!name) {
                alert('Please enter a name for the ping site');
                return;
            }
            
            if (!url) {
                alert('Please enter a URL for the ping site');
                return;
            }
            
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                alert('URL must start with http:// or https://');
                return;
            }
            
            // If editing an existing site with a different name, remove the old one
            if (originalName && originalName !== name) {
                delete pingSites[originalName];
            }
            
            // Add/update the ping site
            pingSites[name] = url;
            
            // Update the UI
            renderPingSitesList();
            
            // Close the modal
            editPingSiteModal.hide();
            
            // Mark as modified
            pingSitesModified = true;
        }
        
        // Remove ping site
        function removePingSite(name) {
            if (confirm(`Are you sure you want to remove the ping site "${name}"?`)) {
                delete pingSites[name];
                
                // Update the UI
                renderPingSitesList();
                
                // Mark as modified
                pingSitesModified = true;
            }
        }
        
        let managePingSitesModal;
        let editPingSiteModal;

        // Generate Pinterest CSV file
        async function generatePinterestCSV() {
            try {
                addLog('Generating Pinterest CSV file...', 'info');

                // Fetch all products
                const response = await fetch('get_products.php?page=1&limit=1000');
                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.error || 'Failed to load products');
                }

                // Generate CSV content
                const csvContent = generateCSVContent(data.products);

                // Create and download file
                downloadCSV(csvContent, 'pinterest_products.csv');

                addLog(`Pinterest CSV generated successfully with ${data.products.length} products`, 'success');

            } catch (error) {
                console.error('Error generating Pinterest CSV:', error);
                addLog(`Error generating Pinterest CSV: ${error.message}`, 'error');
            }
        }

        // Generate CSV content according to Pinterest bulk upload requirements
        function generateCSVContent(products) {
            // CSV Headers according to Pinterest requirements
            const headers = [
                'Title',           // obrigatório - máximo 100 caracteres
                'Media URL', // obrigatório - link público para o ficheiro
                'Pinterest board',  // obrigatório - nome do álbum
                'Description',       // opcional - máximo 500 caracteres
                'Link',            // opcional - URL do produto
                'Keywords'         // opcional - palavras-chave separadas por vírgulas
            ];

            let csvContent = headers.join(',') + '\n';

            products.forEach(product => {
                // Prepare data according to Pinterest requirements
                const title = escapeCSV(product.name.substring(0, 100)); // Max 100 chars

                const imageUrl = product.image_filename ?
                    `https://joaocesarsilva.com/public/assets/images/products/${product.image_filename}` :
                    'https://joaocesarsilva.com/public/assets/images/no-image.png';

                const board = 'JCS Studio Products'; // Default board name

                // Description: SEO description or regular description (max 500 chars)
                let description = product.seo_description || product.description || product.name;
                description = escapeCSV(description.substring(0, 500));

                const productUrl = `https://joaocesarsilva.com/index.php?product=${product.slug}`;

                // Keywords from SEO keywords
                const keywords = escapeCSV(product.seo_keywords || '');

                // Create CSV row
                const row = [
                    title,
                    imageUrl,
                    board,
                    description,
                    productUrl,
                    keywords
                ].join(',');

                csvContent += row + '\n';
            });

            return csvContent;
        }

        // Remove Portuguese accents and special characters
        function removeAccents(text) {
            if (!text) return '';

            const accentMap = {
                'á': 'a', 'à': 'a', 'ã': 'a', 'â': 'a', 'ä': 'a',
                'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e',
                'í': 'i', 'ì': 'i', 'î': 'i', 'ï': 'i',
                'ó': 'o', 'ò': 'o', 'õ': 'o', 'ô': 'o', 'ö': 'o',
                'ú': 'u', 'ù': 'u', 'û': 'u', 'ü': 'u',
                'ç': 'c',
                'ñ': 'n',
                'Á': 'A', 'À': 'A', 'Ã': 'A', 'Â': 'A', 'Ä': 'A',
                'É': 'E', 'È': 'E', 'Ê': 'E', 'Ë': 'E',
                'Í': 'I', 'Ì': 'I', 'Î': 'I', 'Ï': 'I',
                'Ó': 'O', 'Ò': 'O', 'Õ': 'O', 'Ô': 'O', 'Ö': 'O',
                'Ú': 'U', 'Ù': 'U', 'Û': 'U', 'Ü': 'U',
                'Ç': 'C',
                'Ñ': 'N'
            };

            return text.replace(/[áàãâäéèêëíìîïóòõôöúùûüçñÁÀÃÂÄÉÈÊËÍÌÎÏÓÒÕÔÖÚÙÛÜÇÑ]/g, function(match) {
                return accentMap[match] || match;
            });
        }

        // Escape CSV values (handle commas, quotes, newlines) and remove accents
        function escapeCSV(value) {
            if (!value) return '';

            // Convert to string and remove accents
            value = removeAccents(String(value));

            // Escape existing double quotes by doubling them
            value = value.replace(/"/g, '""');

            // If value contains comma, quote, newline, or carriage return, wrap in quotes
            if (value.includes(',') || value.includes('"') || value.includes('\n') || value.includes('\r')) {
                value = '"' + value + '"';
            }

            return value;
        }

        // Download CSV file (ASCII characters only, no encoding issues)
        function downloadCSV(csvContent, filename) {
            // Create blob with standard CSV type
            const blob = new Blob([csvContent], {
                type: 'text/csv;charset=utf-8;'
            });

            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up the URL object
                URL.revokeObjectURL(url);
            }
        }

        // Load products from server
        async function loadProducts(page = 1, limit = 100) {
            currentPage = page;
            try {
                const response = await fetch(`get_products.php?page=${page}&limit=${limit}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.error || 'Failed to load products');
                }

                // Update pagination info
                if (data.pagination) {
                    totalPages = data.pagination.total_pages;
                    updatePaginationInfo(data.pagination);
                }

                // Render products
                renderProducts(data.products);

            } catch (error) {
                addLog(`Error loading products: ${error.message}`, 'error');

                // Show error in the products container
                document.getElementById('productsContainer').innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-danger">
                            Error loading products: ${error.message}
                            <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="loadProducts()">
                                Try Again
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // Render products list
        function renderProducts(products) {
            const productsContainer = document.getElementById('productsContainer');

            if (!products || products.length === 0) {
                productsContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-info">
                            No products found.
                        </div>
                    </div>
                `;
                return;
            }

            let html = '';

            // Helper function to escape HTML attributes
            function escapeHtml(text) {
                if (!text) return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML.replace(/"/g, '&quot;').replace(/'/g, '&#39;');
            }

            products.forEach(product => {
                const imageUrl = product.image_filename ?
                    `/public/assets/images/products/${product.image_filename}` :
                    '/public/assets/images/no-image.png';

                const productUrl = `https://joaocesarsilva.com/index.php?product=${product.slug}`;
                const description = product.seo_description || product.description || product.name;
                const seoKeywords = product.seo_keywords || '';

                // Debug logging for first product only
                if (product.id === products[0].id) {
                    console.log(`Sample Product ${product.id}:`, {
                        name: product.name,
                        slug: product.slug,
                        seo_description: product.seo_description,
                        description: product.description,
                        seo_keywords: product.seo_keywords,
                        final_description: description,
                        final_keywords: seoKeywords
                    });
                }

                html += `
                    <div class="col-md-4 col-lg-3 mb-4">
                        <div class="card h-100 products-card">
                            <img src="${imageUrl}" class="card-img-top" alt="${escapeHtml(product.name)}">
                            <div class="card-body">
                                <h6 class="card-title">${product.name}</h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="product-price">€${parseFloat(product.price).toFixed(2)}</span>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn pinterest-btn btn-sm pinterest-share-btn"
                                                data-name="${escapeHtml(product.name)}"
                                                data-description="${escapeHtml(description)}"
                                                data-keywords="${escapeHtml(seoKeywords)}"
                                                data-product-url="${escapeHtml(productUrl)}"
                                                data-image-url="${escapeHtml(imageUrl)}">
                                            <i class="fab fa-pinterest"></i> Pin It
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                                onclick="debugMetaTags('${escapeHtml(product.slug)}')"
                                                title="Debug Meta Tags">
                                            <i class="fas fa-bug"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            productsContainer.innerHTML = html;

            // Helper function to decode HTML entities
            function decodeHtml(html) {
                const txt = document.createElement('textarea');
                txt.innerHTML = html;
                return txt.value;
            }

            // Add event listeners to Pinterest buttons
            document.querySelectorAll('.pinterest-share-btn').forEach(button => {
                button.addEventListener('click', function() {
                    // Get and decode HTML entities from data attributes
                    const name = decodeHtml(this.getAttribute('data-name') || '');
                    const description = decodeHtml(this.getAttribute('data-description') || '');
                    const keywords = decodeHtml(this.getAttribute('data-keywords') || '');
                    const productUrl = this.getAttribute('data-product-url') || '';
                    const imageUrl = this.getAttribute('data-image-url') || '';

                    console.log('Pinterest Share - Data attributes (decoded):', {
                        name, description, keywords, productUrl, imageUrl
                    });

                    shareOnPinterest(name, description, keywords, productUrl, imageUrl);
                });
            });
        }

        // Update pagination info
        function updatePaginationInfo(pagination) {
            const productsInfo = document.getElementById('productsInfo');
            const productsPagination = document.getElementById('productsPagination');
            const currentPageSpan = document.getElementById('currentPageSpan');
            const prevPage = document.getElementById('prevPage');
            const nextPage = document.getElementById('nextPage');

            // Update info text
            const start = ((pagination.current_page - 1) * pagination.limit) + 1;
            const end = Math.min(pagination.current_page * pagination.limit, pagination.total_products);
            productsInfo.textContent = `Showing ${start}-${end} of ${pagination.total_products} products (Page ${pagination.current_page} of ${pagination.total_pages})`;

            // Update pagination controls
            currentPageSpan.textContent = pagination.current_page;

            // Show/hide pagination if needed
            if (pagination.total_pages > 1) {
                productsPagination.style.display = 'block';

                // Enable/disable prev/next buttons
                if (pagination.has_prev) {
                    prevPage.classList.remove('disabled');
                } else {
                    prevPage.classList.add('disabled');
                }

                if (pagination.has_next) {
                    nextPage.classList.remove('disabled');
                } else {
                    nextPage.classList.add('disabled');
                }
            } else {
                productsPagination.style.display = 'none';
            }
        }

        // Load all products (remove pagination)
        async function loadAllProducts() {
            const loadAllBtn = document.getElementById('loadAllProductsBtn');
            loadAllBtn.disabled = true;
            loadAllBtn.textContent = 'Loading...';

            try {
                await loadProducts(1, 1000); // Load up to 1000 products
                loadAllBtn.textContent = 'All Loaded';
            } catch (error) {
                loadAllBtn.disabled = false;
                loadAllBtn.textContent = 'Load All Products';
                addLog(`Error loading all products: ${error.message}`, 'error');
            }
        }

        // Debug meta tags for a product
        function debugMetaTags(productSlug) {
            const debugUrl = `meta_debug.php?product=${encodeURIComponent(productSlug)}`;
            window.open(debugUrl, 'meta-debug', 'width=1000,height=700,scrollbars=yes,resizable=yes');
        }

        // Test Pinterest sharing with minimal data
        function testPinterestShare() {
            const testUrl = 'https://www.pinterest.com/pin/create/button/?' +
                'url=' + encodeURIComponent('https://www.joaocesarsilva.com/') +
                '&media=' + encodeURIComponent('https://www.joaocesarsilva.com/public/assets/images/logo.png') +
                '&description=' + encodeURIComponent('Test Product - Test description with #hashtag #test');

            console.log('Test Pinterest URL:', testUrl);
            window.open(testUrl, 'pinterest-test', 'width=750,height=350,scrollbars=yes,resizable=yes');
        }

        // Share product on Pinterest
        function shareOnPinterest(productName, description, seoKeywords, productUrl, imageUrl) {
            try {
                // Debug logging
                console.log('Pinterest Share Debug:');
                console.log('Product Name:', productName);
                console.log('Description:', description);
                console.log('SEO Keywords:', seoKeywords);
                console.log('Product URL:', productUrl);
                console.log('Image URL:', imageUrl);

                // Create Pinterest description: Product Title + SEO Description + Hashtags
                let pinterestDescription = productName;

                // Add SEO description if available
                if (description && description.trim() && description !== productName) {
                    pinterestDescription += ' - ' + description.trim();
                }

                // Add hashtags from SEO keywords
                if (seoKeywords && seoKeywords.trim()) {
                    const keywords = seoKeywords.split(',').map(keyword => keyword.trim()).filter(k => k);
                    console.log('Parsed Keywords:', keywords);
                    if (keywords.length > 0) {
                        // Generate hashtags preserving Portuguese characters
                        const hashtags = keywords.map(keyword => {
                            // Remove spaces and some special characters but keep accented characters
                            const cleanKeyword = keyword.replace(/\s+/g, '').replace(/[^\w\u00C0-\u017F]/g, '');
                            return '#' + cleanKeyword;
                        }).join(' ');
                        console.log('Generated Hashtags:', hashtags);
                        pinterestDescription += ' ' + hashtags;
                    }
                } else {
                    console.log('No SEO keywords found or empty');
                }

                console.log('Final Pinterest Description:', pinterestDescription);
                console.log('Description length:', pinterestDescription.length);

                // Get the full URL for the image and product
                const baseUrl = window.location.origin;
                const currentPath = window.location.pathname.replace('/SEO/index.html', '');

                let fullImageUrl = imageUrl;
                if (!imageUrl.startsWith('http')) {
                    fullImageUrl = baseUrl + currentPath + '/' + imageUrl.replace('../', '');
                }

                let fullProductUrl = productUrl;
                if (!productUrl.startsWith('http')) {
                    fullProductUrl = baseUrl + currentPath + '/' + productUrl.replace('../', '');
                }

                // Pinterest share URL using the exact working format from product_detail.php
                const pinterestUrl = 'https://www.pinterest.com/pin/create/button/?' +
                    'url=' + encodeURIComponent(fullProductUrl) +
                    '&media=' + encodeURIComponent(fullImageUrl) +
                    '&description=' + encodeURIComponent(pinterestDescription);

                console.log('Pinterest URL:', pinterestUrl);

                // Test the URL by copying it to clipboard for inspection
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(pinterestUrl).then(() => {
                        console.log('Pinterest URL copied to clipboard for inspection');
                    });
                }

                // Open Pinterest share window
                window.open(pinterestUrl, 'pinterest-share', 'width=750,height=350,scrollbars=yes,resizable=yes');

            } catch (error) {
                console.error('Error sharing on Pinterest:', error);
                alert('Error sharing on Pinterest. Please try again.');
            }
        }

        // Initialize when DOM is loaded
        window.onload = function() {
            // Initialize Bootstrap modals
            managePingSitesModal = new bootstrap.Modal(document.getElementById('managePingSitesModal'));
            editPingSiteModal = new bootstrap.Modal(document.getElementById('editPingSiteModal'));
            
            // Initialize range slider
            document.getElementById('urlLimit').addEventListener('input', function() {
                document.getElementById('urlLimitValue').textContent = this.value;
            });
            
            // Load ping sites
            loadPingSites();
            
            // Add log entry
            addLog('System initialized and ready to use', 'info');
        };

        // Add log entry
        function addLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type} fade-in`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('activityLog').appendChild(logEntry);
            document.getElementById('activityLog').scrollTop = document.getElementById('activityLog').scrollHeight;
        }

        // Update progress
        function updateProgress(current, total) {
            const percent = total > 0 ? Math.round((current / total) * 100) : 0;
            document.getElementById('progressBar').style.width = `${percent}%`;
            document.getElementById('progressText').textContent = `${percent}%`;
        }

        // Update stats display
        function updateStats() {
            document.getElementById('urlsProcessed').textContent = stats.urlsProcessed;
            document.getElementById('urlsSkipped').textContent = stats.urlsSkipped;
            document.getElementById('successPings').textContent = stats.successPings;
            document.getElementById('failedPings').textContent = stats.failedPings;
        }

        // Add result item
        function addResultItem(url, backlinkSite, backlinkUrl, success, httpCode, error, duration, attempts, isNonResponsive) {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item fade-in';
            
            const statusClass = success ? 'badge-success' : 'badge-danger';
            const statusText = success ? 'Success' : 'Failed';
            
            resultItem.innerHTML = `
                <div class="result-header">
                    <div class="result-url">${url}</div>
                    <span class="badge ${statusClass}">${statusText}</span>
                </div>
                <div class="result-details">
                    <p><strong>Backlink Site:</strong> ${backlinkSite} (${backlinkUrl})</p>
                    ${!success ? `<p class="text-danger"><strong>Error:</strong> ${error || `HTTP Code: ${httpCode}`}</p>` : ''}
                    <p><strong>HTTP Code:</strong> ${httpCode}</p>
                    <p><strong>Attempts:</strong> ${attempts}</p>
                    <p><strong>Duration:</strong> ${duration}s</p>
                </div>
                ${isNonResponsive ? `
                <div class="non-responsive">
                    <strong>Warning:</strong> This backlink site appears to be non-responsive. 
                    Consider removing it from your backlink sites list.
                </div>` : ''}
            `;
            
            // Clear initial message if it's the first result
            const resultsContainer = document.getElementById('resultsContainer');
            if (resultsContainer.querySelector('.text-muted')) {
                resultsContainer.innerHTML = '';
            }
            
            resultsContainer.prepend(resultItem);
            
            // If failed, add to failed links list
            if (!success) {
                addFailedLink(url, backlinkSite, backlinkUrl, error || `HTTP Code: ${httpCode}`);
            }
        }

        // Add to failed links list
        function addFailedLink(url, backlinkSite, backlinkUrl, error) {
            // Check if already in the list
            if (failedLinks.some(link => link.url === url && link.site === backlinkSite)) {
                return;
            }
            
            failedLinks.push({ url, site: backlinkSite, siteUrl: backlinkUrl, error });
            
            // Clear initial message if it's the first failed link
            const failedLinksContainer = document.getElementById('failedLinksContainer');
            if (failedLinksContainer.querySelector('.text-muted')) {
                failedLinksContainer.innerHTML = `
                    <h5 class="mb-3">Failed Links</h5>
                    <div class="failed-links-list"></div>
                `;
            }
            
            const failedLinksList = failedLinksContainer.querySelector('.failed-links-list');
            
            const listItem = document.createElement('div');
            listItem.className = 'result-item fade-in';
            listItem.innerHTML = `
                <div class="result-header">
                    <div class="result-url">${url}</div>
                </div>
                <div class="result-details">
                    <p><strong>Backlink Site:</strong> ${backlinkSite} (${backlinkUrl})</p>
                    <p class="text-danger"><strong>Error:</strong> ${error}</p>
                </div>
            `;
            
            failedLinksList.appendChild(listItem);
            
            // Switch to the failed links tab to show the user
            document.getElementById('failed-tab').classList.add('position-relative');
            
            // Check if badge already exists
            let badge = document.getElementById('failed-tab').querySelector('.badge');
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                badge.style.fontSize = '0.6rem';
                badge.style.marginTop = '-5px';
                badge.style.marginLeft = '-10px';
                document.getElementById('failed-tab').appendChild(badge);
            }
            
            badge.textContent = failedLinks.length;
        }

        // Add to non-responsive sites list
        function addNonResponsiveSite(siteName, siteUrl, error) {
            // Check if already in the list
            if (nonResponsiveSites.some(site => site.name === siteName)) {
                return;
            }
            
            nonResponsiveSites.push({ name: siteName, url: siteUrl, error });
            
            // Clear initial message if it's the first non-responsive site
            const nonResponsiveSitesContainer = document.getElementById('nonResponsiveSitesContainer');
            if (nonResponsiveSitesContainer.querySelector('.text-muted')) {
                nonResponsiveSitesContainer.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">Non-Responsive Ping Sites</h5>
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeAllNonResponsiveSites()">
                            <i class="fas fa-trash-alt"></i> Remove All
                        </button>
                    </div>
                    <p>The following backlink sites were non-responsive and should be considered for removal:</p>
                    <div class="list-group non-responsive-sites-list"></div>
                `;
            }
            
            const nonResponsiveSitesList = nonResponsiveSitesContainer.querySelector('.non-responsive-sites-list');
            
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center fade-in';
            listItem.innerHTML = `
                <div>
                    <div><strong>${siteName}</strong></div>
                    <div class="small">${siteUrl}</div>
                    <div class="small text-danger">Error: ${error}</div>
                </div>
                <button type="button" class="btn btn-danger btn-sm" onclick="removeNonResponsiveSite('${siteName}')">
                    <i class="fas fa-trash-alt"></i>
                </button>
            `;
            
            nonResponsiveSitesList.appendChild(listItem);
            
            // Switch to the non-responsive sites tab to show the user
            document.getElementById('non-responsive-tab').classList.add('position-relative');
            
            // Check if badge already exists
            let badge = document.getElementById('non-responsive-tab').querySelector('.badge');
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                badge.style.fontSize = '0.6rem';
                badge.style.marginTop = '-5px';
                badge.style.marginLeft = '-10px';
                document.getElementById('non-responsive-tab').appendChild(badge);
            }
            
            badge.textContent = nonResponsiveSites.length;
        }

        // Remove a non-responsive site
        function removeNonResponsiveSite(siteName) {
            // Remove from the array
            nonResponsiveSites = nonResponsiveSites.filter(site => site.name !== siteName);
            
            // Remove from the ping sites list
            removePingSite(siteName);
            
            // Update the UI
            const nonResponsiveSitesContainer = document.getElementById('nonResponsiveSitesContainer');
            const nonResponsiveSitesList = nonResponsiveSitesContainer.querySelector('.non-responsive-sites-list');
            
            if (nonResponsiveSitesList) {
                const listItems = nonResponsiveSitesList.querySelectorAll('.list-group-item');
                for (let i = 0; i < listItems.length; i++) {
                    const item = listItems[i];
                    const itemName = item.querySelector('strong').textContent;
                    if (itemName === siteName) {
                        item.remove();
                        break;
                    }
                }
            }
            
            // Update the badge
            const badge = document.getElementById('non-responsive-tab').querySelector('.badge');
            if (badge) {
                if (nonResponsiveSites.length > 0) {
                    badge.textContent = nonResponsiveSites.length;
                } else {
                    badge.remove();
                    // Reset the container if no more non-responsive sites
                    nonResponsiveSitesContainer.innerHTML = `
                        <div class="text-center text-muted py-5">Non-responsive ping sites will appear here during processing</div>
                    `;
                }
            }
            
            addLog(`Removed non-responsive ping site: ${siteName}`, 'success');
        }

        // Remove all non-responsive sites
        function removeAllNonResponsiveSites() {
            if (nonResponsiveSites.length === 0) {
                return;
            }
            
            if (!confirm('Are you sure you want to remove all non-responsive ping sites?')) {
                return;
            }
            
            // Remove each site from the ping sites list
            nonResponsiveSites.forEach(site => {
                removePingSite(site.name);
            });
            
            // Clear the array
            nonResponsiveSites = [];
            
            // Reset the UI
            const nonResponsiveSitesContainer = document.getElementById('nonResponsiveSitesContainer');
            nonResponsiveSitesContainer.innerHTML = `
                <div class="text-center text-muted py-5">Non-responsive ping sites will appear here during processing</div>
            `;
            
            // Remove the badge
            const badge = document.getElementById('non-responsive-tab').querySelector('.badge');
            if (badge) {
                badge.remove();
            }
            
            addLog('Removed all non-responsive ping sites', 'success');
            
            // Update the ping sites list
            renderPingSitesList();
        }

        // Add to processed URLs list
        function addProcessedUrl(url, status) {
            // Clear initial message if it's the first processed URL
            const processedUrlsContainer = document.getElementById('processedUrlsContainer');
            if (processedUrlsContainer.querySelector('.text-muted')) {
                processedUrlsContainer.innerHTML = `
                    <h5 class="mb-3">Processed URLs</h5>
                    <div class="list-group processed-urls-list"></div>
                `;
            }
            
            const processedUrlsList = processedUrlsContainer.querySelector('.processed-urls-list');
            
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item fade-in';
            
            if (status === 'skipped') {
                listItem.innerHTML = `<span class="text-warning"><i class="fas fa-exclamation-triangle"></i> ${url} (skipped - known to cause 503 errors)</span>`;
            } else if (status === 'failed') {
                listItem.innerHTML = `<span class="text-danger"><i class="fas fa-times-circle"></i> ${url} (failed)</span>`;
            } else {
                listItem.innerHTML = `<span class="text-success"><i class="fas fa-check-circle"></i> ${url}</span>`;
            }
            
            processedUrlsList.appendChild(listItem);
        }

        // Load ping sites
        async function loadPingSites() {
            try {
                const response = await fetch('ajax_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_ping_sites'
                    }),
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to load ping sites');
                }
                
                pingSites = data.sites;
                renderPingSitesList();
                
                addLog(`Loaded ${Object.keys(pingSites).length} ping sites`, 'success');
                return true;
                
            } catch (error) {
                addLog(`Error loading ping sites: ${error.message}`, 'error');
                return false;
            }
        }

        // Render ping sites list
        function renderPingSitesList() {
            const pingSitesList = document.getElementById('pingSitesList');
            pingSitesList.innerHTML = '';
            
            if (Object.keys(pingSites).length === 0) {
                pingSitesList.innerHTML = `
                    <div class="text-center text-muted py-5">No ping sites found. Add some using the button above.</div>
                `;
                return;
            }
            
            for (const [siteName, siteUrl] of Object.entries(pingSites)) {
                const isNonResponsive = nonResponsiveSites.some(site => site.name === siteName);
                
                const listItem = document.createElement('div');
                listItem.className = 'ping-site-item fade-in';
                listItem.innerHTML = `
                    <div>
                        <div class="ping-site-name">
                            <span class="ping-site-status ${isNonResponsive ? 'inactive' : 'active'}"></span>
                            ${siteName}
                        </div>
                        <div class="ping-site-url">${siteUrl}</div>
                    </div>
                    <div class="ping-site-actions">
                        <i class="fas fa-edit action-icon edit" onclick="editPingSite('${siteName}')"></i>
                        <i class="fas fa-trash-alt action-icon delete" onclick="confirmRemovePingSite('${siteName}')"></i>
                    </div>
                `;
                
                pingSitesList.appendChild(listItem);
            }
        }

        // Confirm remove ping site
        function confirmRemovePingSite(siteName) {
            if (confirm(`Are you sure you want to remove the ping site "${siteName}"?`)) {
                removePingSite(siteName);
            }
        }

        // Edit ping site
        function editPingSite(siteName) {
            const siteUrl = pingSites[siteName];
            
            document.getElementById('pingSiteIndex').value = siteName;
            document.getElementById('pingSiteName').value = siteName;
            document.getElementById('pingSiteUrl').value = siteUrl;
            
            document.getElementById('editPingSiteModalLabel').textContent = 'Edit Ping Site';
            
            editPingSiteModal.show();
        }

        // Add new ping site
        function addNewPingSite() {
            document.getElementById('pingSiteIndex').value = -1;
            document.getElementById('pingSiteName').value = '';
            document.getElementById('pingSiteUrl').value = '';
            
            document.getElementById('editPingSiteModalLabel').textContent = 'Add Ping Site';
            
            editPingSiteModal.show();
        }

        // Save ping site
        function savePingSite() {
            const index = document.getElementById('pingSiteIndex').value;
            const name = document.getElementById('pingSiteName').value.trim();
            const url = document.getElementById('pingSiteUrl').value.trim();
            
            if (!name || !url) {
                alert('Please fill in all fields');
                return;
            }
            
            // If editing, remove the old entry
            if (index !== -1 && index !== name) {
                delete pingSites[index];
            }
            
            // Add the new entry
            pingSites[name] = url;
            pingSitesModified = true;
            
            // Update the UI
            renderPingSitesList();
            
            editPingSiteModal.hide();
            
            addLog(`${index === -1 ? 'Added' : 'Updated'} ping site: ${name}`, 'success');
        }

        // Remove ping site
        function removePingSite(siteName) {
            delete pingSites[siteName];
            pingSitesModified = true;
            
            // Update the UI
            renderPingSitesList();
            
            addLog(`Removed ping site: ${siteName}`, 'success');
        }

        // Save ping sites to server
        async function savePingSitesToServer() {
            if (!pingSitesModified) {
                managePingSitesModal.hide();
                return true;
            }
            
            try {
                const response = await fetch('ajax_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save_ping_sites',
                        sites: pingSites
                    }),
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to save ping sites');
                }
                
                pingSitesModified = false;
                addLog('Ping sites saved successfully', 'success');
                
                managePingSitesModal.hide();
                return true;
                
            } catch (error) {
                addLog(`Error saving ping sites: ${error.message}`, 'error');
                return false;
            }
        }

        // Show select URLs modal
        async function showSelectUrlsModal() {
            // Clear previous content
            const urlSelectionList = document.getElementById('urlSelectionList');
            
            // Check if we have URLs to display
            if (allExtractedUrls.length === 0) {
                // Show loading message
                urlSelectionList.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        Fetching URLs automatically, please wait...
                    </div>
                `;
                
                // Show the modal while fetching
                const selectUrlsModal = new bootstrap.Modal(document.getElementById('selectUrlsModal'));
                selectUrlsModal.show();
                
                // Get values from form
                const feedUrl = document.getElementById('feedUrl').value;
                const sitemapUrl = document.getElementById('sitemapUrl').value;
                const urlLimit = document.getElementById('urlLimit').value;
                
                try {
                    // Fetch URLs directly via AJAX
                    const response = await fetch('ajax_handler.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'fetch_urls',
                            feedUrl,
                            sitemapUrl,
                            maxUrlsToProcess: parseInt(urlLimit)
                        }),
                    });
                    
                    const data = await response.json();
                    
                    if (!data.success) {
                        throw new Error(data.error || 'Failed to fetch URLs');
                    }
                    
                    // Store all extracted URLs for the selection modal
                    allExtractedUrls = [...data.urls];
                    
                    // Generate checkboxes for each URL
                    let checkboxesHtml = '';
                    allExtractedUrls.forEach((url, index) => {
                        const isChecked = selectedUrls.includes(url) ? 'checked' : '';
                        checkboxesHtml += `
                            <div class="form-check mb-2">
                                <input class="form-check-input url-checkbox" type="checkbox" value="${url}" id="url-${index}" ${isChecked}>
                                <label class="form-check-label text-break" for="url-${index}">
                                    ${url}
                                </label>
                            </div>
                        `;
                    });
                    
                    // Update the modal with the fetched URLs
                    urlSelectionList.innerHTML = checkboxesHtml;
                    
                    // Add a silent log entry
                    addLog(`Automatically fetched ${data.totalCount} URLs for selection`, 'info');
                    
                    // Update the URL limit slider based on the number of extracted URLs
                    const urlLimitSlider = document.getElementById('urlLimit');
                    const maxUrls = Math.max(1, data.totalCount);
                    urlLimitSlider.max = maxUrls;
                    
                    // If current value is greater than the new max, adjust it
                    if (parseInt(urlLimitSlider.value) > maxUrls) {
                        urlLimitSlider.value = maxUrls;
                    }
                    
                    // Update the display of the current value
                    document.getElementById('urlLimitValue').textContent = urlLimitSlider.value;
                    
                } catch (error) {
                    // Show error message in the modal
                    urlSelectionList.innerHTML = `
                        <div class="text-center text-danger py-5">
                            Error fetching URLs: ${error.message}
                            <div class="mt-3">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="fetchUrls().then(() => showSelectUrlsModal())">
                                    Try Again
                                </button>
                            </div>
                        </div>
                    `;
                    addLog(`Error automatically fetching URLs: ${error.message}`, 'error');
                }
                
                return;
            }
            
            // If we already have URLs, generate checkboxes for each URL
            let checkboxesHtml = '';
            allExtractedUrls.forEach((url, index) => {
                const isChecked = selectedUrls.includes(url) ? 'checked' : '';
                checkboxesHtml += `
                    <div class="form-check mb-2">
                        <input class="form-check-input url-checkbox" type="checkbox" value="${url}" id="url-${index}" ${isChecked}>
                        <label class="form-check-label text-break" for="url-${index}">
                            ${url}
                        </label>
                    </div>
                `;
            });
            
            urlSelectionList.innerHTML = checkboxesHtml;
            
            // Show the modal
            const selectUrlsModal = new bootstrap.Modal(document.getElementById('selectUrlsModal'));
            selectUrlsModal.show();
        }
        
        // Select all URLs
        function selectAllUrls() {
            const checkboxes = document.querySelectorAll('.url-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }
        
        // Deselect all URLs
        function deselectAllUrls() {
            const checkboxes = document.querySelectorAll('.url-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }
        
        // Mark selected URLs to skip
        function markSelectedUrlsToSkip() {
            const checkboxes = document.querySelectorAll('.url-checkbox:checked');
            const selectedToSkip = Array.from(checkboxes).map(checkbox => checkbox.value);
            
            if (selectedToSkip.length === 0) {
                addLog('No URLs selected to skip', 'warning');
                return;
            }
            
            // Add to skipped URLs list
            skippedUrls = [...skippedUrls, ...selectedToSkip];
            
            // Remove duplicates
            skippedUrls = [...new Set(skippedUrls)];
            
            // Update the UI to show skipped status
            updateUrlSelectionList();
            
            addLog(`Marked ${selectedToSkip.length} URLs to be skipped during processing`, 'info');
        }
        
        // Update URL selection list with skip status
        function updateUrlSelectionList() {
            const urlSelectionList = document.getElementById('urlSelectionList');
            
            // If no URLs, return
            if (allExtractedUrls.length === 0) {
                return;
            }
            
            // Generate checkboxes for each URL
            let checkboxesHtml = '';
            allExtractedUrls.forEach((url, index) => {
                const isChecked = selectedUrls.includes(url) ? 'checked' : '';
                const isSkipped = skippedUrls.includes(url);
                const skipClass = isSkipped ? 'text-decoration-line-through text-muted' : '';
                const skipBadge = isSkipped ? '<span class="badge bg-danger ms-2">Skipped</span>' : '';
                
                checkboxesHtml += `
                    <div class="form-check mb-2">
                        <input class="form-check-input url-checkbox" type="checkbox" value="${url}" id="url-${index}" ${isChecked} ${isSkipped ? 'disabled' : ''}>
                        <label class="form-check-label text-break ${skipClass}" for="url-${index}">
                            ${url} ${skipBadge}
                        </label>
                    </div>
                `;
            });
            
            urlSelectionList.innerHTML = checkboxesHtml;
        }
        
        // Apply URL selection
        function applyUrlSelection() {
            const checkboxes = document.querySelectorAll('.url-checkbox:checked:not([disabled])');
            selectedUrls = Array.from(checkboxes).map(checkbox => checkbox.value);
            
            // Close the modal
            const selectUrlsModal = bootstrap.Modal.getInstance(document.getElementById('selectUrlsModal'));
            selectUrlsModal.hide();
            
            // Show confirmation message
            addLog(`Selected ${selectedUrls.length} URLs for processing`, 'info');
        }

        // Fetch URLs from feed and sitemap
        async function fetchUrls() {
            const feedUrl = document.getElementById('feedUrl').value;
            const sitemapUrl = document.getElementById('sitemapUrl').value;
            const urlLimit = document.getElementById('urlLimit').value;
            
            addLog(`Fetching URLs from feed (${feedUrl}) and sitemap (${sitemapUrl})...`);
            document.getElementById('currentTask').textContent = 'Fetching URLs...';
            
            try {
                const response = await fetch('ajax_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'fetch_urls',
                        feedUrl,
                        sitemapUrl,
                        maxUrlsToProcess: parseInt(urlLimit)
                    }),
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to fetch URLs');
                }
                
                addLog(`Found ${data.totalCount} total URLs (${data.feedCount} from feed, ${data.sitemapCount} from sitemap)`, 'success');
                
                if (data.skippedCount > 0) {
                    addLog(`Skipped ${data.skippedCount} problematic URLs`, 'warning');
                    stats.urlsSkipped = data.skippedCount;
                    updateStats();
                    
                    // Add skipped URLs to the processed list
                    data.skippedUrls.forEach(url => {
                        addProcessedUrl(url, 'skipped');
                    });
                }
                
                if (data.limitApplied) {
                    addLog(`Limiting to ${data.maxUrlsToProcess} URLs as per configuration`, 'info');
                }
                
                // Store all extracted URLs for the selection modal
                allExtractedUrls = [...data.urls];
                
                // If we have selected URLs and they're still valid, use them
                if (selectedUrls.length > 0) {
                    // Filter to only include URLs that are still in the extracted list
                    selectedUrls = selectedUrls.filter(url => allExtractedUrls.includes(url));
                    
                    if (selectedUrls.length > 0) {
                        urls = [...selectedUrls]; // Use selected URLs
                        addLog(`Using ${selectedUrls.length} previously selected URLs`, 'info');
                    } else {
                        urls = [...data.urls]; // Reset to all URLs if selection is empty
                        selectedUrls = []; // Clear the selection
                    }
                } else {
                    urls = [...data.urls]; // Use all URLs by default
                }
                
                totalUrls = urls.length;
                
                // Randomize the URLs array
                addLog(`Randomizing URLs for better distribution...`, 'info');
                urls = shuffleArray(urls);
                
                // Update the URL limit slider based on the number of extracted URLs
                const extractedUrlCount = data.totalCount;
                
                // Update only the slider max value, not the current value
                const urlLimitSlider = document.getElementById('urlLimit');
                
                // Set max to exactly the number of extracted URLs, but ensure it's at least 1
                const maxUrls = Math.max(1, extractedUrlCount);
                urlLimitSlider.max = maxUrls;
                
                // If current value is greater than the new max, adjust it
                if (parseInt(urlLimitSlider.value) > maxUrls) {
                    urlLimitSlider.value = maxUrls;
                }
                
                // Update the display of the current value
                document.getElementById('urlLimitValue').textContent = urlLimitSlider.value;
                
                addLog(`Set maximum URLs to process limit to ${maxUrls} (total extracted URLs)`, 'info');
                addLog(`Ready to process ${totalUrls} URLs`, 'success');
                return true;
                
            } catch (error) {
                addLog(`Error: ${error.message}`, 'error');
                return false;
            }
        }

        // Process a single URL with random backlink sites
        async function processUrl(url) {
            addLog(`Processing URL: ${url}`, 'info');
            document.getElementById('currentTask').textContent = `Processing: ${url}`;
            
            try {
                // Get the number of backlink generators to use per URL from the slider
                const backlinkGeneratorsCount = parseInt(document.getElementById('backlinkGeneratorsPerUrl').value);
                
                // First get random backlink sites
                const sitesResponse = await fetch('ajax_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_random_backlink_sites',
                        count: backlinkGeneratorsCount // Use the value from the slider
                    }),
                });
                
                const sitesData = await sitesResponse.json();
                
                if (!sitesData.success) {
                    throw new Error(sitesData.error || 'Failed to get backlink sites');
                }
                
                const selectedSites = sitesData.sites;
                addLog(`Selected ${Object.keys(selectedSites).length} random backlink sites for this URL`, 'info');
                
                let urlSuccess = false;
                
                // Process each backlink site
                for (const [siteName, siteUrl] of Object.entries(selectedSites)) {
                    if (shouldStop) break;
                    
                    addLog(`Pinging to ${siteName} (${siteUrl})...`, 'info');
                    
                    const pingResponse = await fetch('ajax_handler.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'ping_url',
                            url,
                            backlinkSite: siteName,
                            backlinkUrl: siteUrl
                        }),
                    });
                    
                    const pingData = await pingResponse.json();
                    
                    if (pingData.success) {
                        addLog(`Success! HTTP Code: ${pingData.httpCode} (took ${pingData.duration}s)`, 'success');
                        stats.successPings++;
                        urlSuccess = true;
                        
                        addResultItem(
                            url,
                            siteName,
                            siteUrl,
                            true,
                            pingData.httpCode,
                            null,
                            pingData.duration,
                            pingData.attempts,
                            false
                        );
                    } else {
                        const errorMsg = pingData.error || `HTTP Code: ${pingData.httpCode}`;
                        addLog(`Error: ${errorMsg} (took ${pingData.duration}s)`, 'error');
                        stats.failedPings++;
                        
                        const isNonResponsive = pingData.isNonResponsive;
                        
                        addResultItem(
                            url,
                            siteName,
                            siteUrl,
                            false,
                            pingData.httpCode,
                            errorMsg,
                            pingData.duration,
                            pingData.attempts,
                            isNonResponsive
                        );
                        
                        if (isNonResponsive) {
                            addNonResponsiveSite(siteName, siteUrl, errorMsg);
                        }
                    }
                    
                    updateStats();
                    
                    // Add a small delay between pings
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                stats.urlsProcessed++;
                updateStats();
                addProcessedUrl(url, urlSuccess ? 'processed' : 'failed');
                
                return true;
            } catch (error) {
                addLog(`Error processing URL ${url}: ${error.message}`, 'error');
                return false;
            }
        }

        // Main process function with parallel processing
        async function startProcess() {
            if (isProcessing) {
                return;
            }
            
            isProcessing = true;
            shouldStop = false;
            currentUrlIndex = 0;
            
            // Get the number of parallel workers from the slider
            const backlinkParallelWorkers = parseInt(document.getElementById('backlinkParallelWorkers').value);
            
            // Reset stats
            stats = {
                urlsProcessed: 0,
                urlsSkipped: 0,
                successPings: 0,
                failedPings: 0
            };
            updateStats();
            
            // Reset UI
            document.getElementById('resultsContainer').innerHTML = '<div class="text-center text-muted py-5">Results will appear here as URLs are processed</div>';
            document.getElementById('failedLinksContainer').innerHTML = '<div class="text-center text-muted py-5">Failed links will appear here during processing</div>';
            document.getElementById('nonResponsiveSitesContainer').innerHTML = '<div class="text-center text-muted py-5">Non-responsive ping sites will appear here during processing</div>';
            document.getElementById('processedUrlsContainer').innerHTML = '<div class="text-center text-muted py-5">Processed URLs will appear here during processing</div>';
            
            // Remove badges
            const failedBadge = document.getElementById('failed-tab').querySelector('.badge');
            if (failedBadge) failedBadge.remove();
            
            const nonResponsiveBadge = document.getElementById('non-responsive-tab').querySelector('.badge');
            if (nonResponsiveBadge) nonResponsiveBadge.remove();
            
            // Reset arrays
            nonResponsiveSites = [];
            failedLinks = [];
            
            // Update UI state
            document.getElementById('startButton').disabled = true;
            document.getElementById('stopButton').disabled = false;
            
            // Fetch URLs
            const success = await fetchUrls();
            if (!success) {
                finishProcess();
                return;
            }
            
            addLog(`Starting parallel processing with ${backlinkParallelWorkers} workers...`, 'info');
            
            // Process URLs in parallel batches
            let processedCount = 0;
            
            while (currentUrlIndex < urls.length && !shouldStop) {
                const batch = [];
                const batchSize = Math.min(backlinkParallelWorkers, urls.length - currentUrlIndex);
                
                // Create a batch of promises
                for (let i = 0; i < batchSize; i++) {
                    if (currentUrlIndex < urls.length) {
                        const url = urls[currentUrlIndex];
                        batch.push(processUrl(url));
                        currentUrlIndex++;
                    }
                }
                
                // Wait for all promises in the batch to complete
                await Promise.all(batch);
                
                // Update progress
                processedCount += batchSize;
                updateProgress(processedCount, totalUrls);
                
                // Add a small delay between batches to prevent overwhelming the server
                if (!shouldStop && currentUrlIndex < urls.length) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
            
            finishProcess();
        }

        // Stop process
        function stopProcess() {
            if (!isProcessing) {
                return;
            }
            
            shouldStop = true;
            document.getElementById('stopButton').disabled = true;
            addLog('Stopping process after current URL completes...', 'warning');
        }

        // Finish process
        function finishProcess() {
            isProcessing = false;
            document.getElementById('startButton').disabled = false;
            document.getElementById('stopButton').disabled = true;
            
            if (shouldStop) {
                document.getElementById('currentTask').textContent = 'Process stopped by user';
            } else {
                document.getElementById('currentTask').textContent = 'Process completed successfully';
                updateProgress(totalUrls, totalUrls);
            }
            
            addLog(`Process ${shouldStop ? 'stopped' : 'completed'} with ${stats.urlsProcessed} URLs processed, ${stats.successPings} successful pings, and ${stats.failedPings} failed pings.`, shouldStop ? 'warning' : 'success');
            
            // Save any modified ping sites
            if (pingSitesModified) {
                savePingSitesToServer();
            }
        }

        // Run IndexNow function - direct call to ping.php
        async function runIndexNow() {
            try {
                // Disable the button to prevent multiple clicks
                document.getElementById('indexNowButton').disabled = true;
                
                // Update UI
                document.getElementById('currentTask').textContent = 'Running IndexNow...';
                addLog('Starting IndexNow submission process...', 'info');
                
                // Create a new XMLHttpRequest to fetch the ping.php content
                const xhr = new XMLHttpRequest();
                xhr.open('GET', 'ping.php', true);
                
                // Set up event handlers
                xhr.onprogress = function(event) {
                    // This will be called periodically as data is received
                    if (event.lengthComputable) {
                        const percentComplete = Math.round((event.loaded / event.total) * 100);
                        updateProgress(percentComplete, 100);
                    }
                };
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        // Create a temporary div to parse the HTML response
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = xhr.responseText;
                        
                        // Extract the results
                        const h2Elements = tempDiv.getElementsByTagName('h2');
                        const h3Elements = tempDiv.getElementsByTagName('h3');
                        
                        if (h2Elements.length > 0) {
                            addLog(h2Elements[0].textContent, 'info');
                        }
                        
                        // Process each search engine result
                        const strongElements = tempDiv.getElementsByTagName('strong');
                        for (let i = 0; i < strongElements.length; i++) {
                            const engineName = strongElements[i].textContent;
                            let resultText = '';
                            
                            // Get the text following this strong element until the next br
                            let node = strongElements[i].nextSibling;
                            while (node && node.nodeName !== 'BR') {
                                if (node.nodeType === Node.TEXT_NODE) {
                                    resultText += node.textContent;
                                } else if (node.nodeType === Node.ELEMENT_NODE) {
                                    resultText += node.textContent;
                                }
                                node = node.nextSibling;
                            }
                            
                            // Add log entry for this search engine
                            if (resultText.includes('HTTP Code: 200')) {
                                addLog(`${engineName}: ${resultText.trim()}`, 'success');
                            } else if (resultText.includes('Error:')) {
                                addLog(`${engineName}: ${resultText.trim()}`, 'error');
                            } else {
                                addLog(`${engineName}: ${resultText.trim()}`, 'info');
                            }
                        }
                        
                        // Add submitted URLs
                        if (h3Elements.length > 0) {
                            addLog(h3Elements[0].textContent, 'info');
                            
                            // Get all URLs
                            const urls = [];
                            let currentNode = h3Elements[0].nextSibling;
                            while (currentNode) {
                                if (currentNode.nodeName === 'BR') {
                                    const urlText = currentNode.previousSibling?.textContent?.trim();
                                    if (urlText && urlText.startsWith('http')) {
                                        urls.push(urlText);
                                    }
                                }
                                currentNode = currentNode.nextSibling;
                            }
                            
                            // Log the first 5 URLs and a count message if there are more
                            const urlsToShow = urls.slice(0, 5);
                            urlsToShow.forEach(url => {
                                addLog(`Submitted URL: ${url}`, 'info');
                            });
                            
                            if (urls.length > 5) {
                                addLog(`... and ${urls.length - 5} more URLs submitted.`, 'info');
                            }
                        }
                        
                        // Update UI to show completion
                        document.getElementById('currentTask').textContent = 'IndexNow submission completed';
                        updateProgress(100, 100);
                        addLog('IndexNow submission process completed successfully!', 'success');
                    } else {
                        // Handle error
                        addLog(`Error: Failed to run IndexNow. Status code: ${xhr.status}`, 'error');
                        document.getElementById('currentTask').textContent = 'IndexNow submission failed';
                    }
                    
                    // Re-enable the button
                    document.getElementById('indexNowButton').disabled = false;
                };
                
                xhr.onerror = function() {
                    // Handle network error
                    addLog('Error: Network error occurred while running IndexNow.', 'error');
                    document.getElementById('currentTask').textContent = 'IndexNow submission failed';
                    document.getElementById('indexNowButton').disabled = false;
                };
                
                // Send the request
                xhr.send();
                
            } catch (error) {
                // Handle any unexpected errors
                addLog(`Error: ${error.message}`, 'error');
                document.getElementById('currentTask').textContent = 'IndexNow submission failed';
                document.getElementById('indexNowButton').disabled = false;
            }
        }
    </script>
</body>
</html>
