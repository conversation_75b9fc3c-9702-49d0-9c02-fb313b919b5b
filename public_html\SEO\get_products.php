<?php
// Include necessary files
require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Get products with their images and SEO data
    $sql = "SELECT 
                p.id,
                p.name_pt as name,
                p.slug,
                p.description_pt as description,
                p.base_price as price,
                p.seo_title,
                p.seo_description,
                p.seo_keywords,
                pi.filename as image_filename
            FROM products p
            LEFT JOIN (
                SELECT 
                    product_id, 
                    filename, 
                    ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY is_default DESC, id ASC) as rn
                FROM product_images
            ) pi ON p.id = pi.product_id AND pi.rn = 1
            WHERE p.is_active = 1
            ORDER BY p.name_pt ASC
            LIMIT 50";
    
    $products = db_query($sql, [], false, true);
    
    if ($products === false) {
        throw new Exception('Failed to fetch products from database');
    }
    
    // Ensure products is an array
    if (!is_array($products)) {
        $products = [];
    }
    
    // Process products to ensure proper data types and handle nulls
    $processed_products = [];
    foreach ($products as $product) {
        $processed_products[] = [
            'id' => (int)$product['id'],
            'name' => $product['name'] ?? 'Unnamed Product',
            'slug' => $product['slug'] ?? '',
            'description' => $product['description'] ?? '',
            'price' => (float)($product['price'] ?? 0),
            'seo_title' => $product['seo_title'] ?? '',
            'seo_description' => $product['seo_description'] ?? '',
            'seo_keywords' => $product['seo_keywords'] ?? '',
            'image_filename' => $product['image_filename'] ?? null
        ];
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'products' => $processed_products,
        'count' => count($processed_products)
    ]);
    
} catch (Exception $e) {
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'products' => []
    ]);
}
?>
