<?php
// Disable any output buffering and error display
ob_start();
ini_set('display_errors', 0);
error_reporting(0);

// Set content type to JSON
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Database path
    $db_path = __DIR__ . '/../../dbjcs2112ew.sqlite';

    if (!file_exists($db_path)) {
        throw new Exception('Database file not found');
    }

    // Create PDO connection
    $pdo = new PDO('sqlite:' . $db_path);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get products with their images and SEO data
    $sql = "SELECT
                p.id,
                p.name_pt as name,
                p.slug,
                p.description_pt as description,
                p.base_price as price,
                p.seo_title,
                p.seo_description,
                p.seo_keywords,
                pi.filename as image_filename
            FROM products p
            LEFT JOIN (
                SELECT
                    product_id,
                    filename,
                    ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY is_default DESC, id ASC) as rn
                FROM product_images
            ) pi ON p.id = pi.product_id AND pi.rn = 1
            WHERE p.is_active = 1
            ORDER BY p.name_pt ASC
            LIMIT 50";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Process products to ensure proper data types and handle nulls
    $processed_products = [];
    foreach ($products as $product) {
        $processed_products[] = [
            'id' => (int)$product['id'],
            'name' => $product['name'] ?? 'Unnamed Product',
            'slug' => $product['slug'] ?? '',
            'description' => $product['description'] ?? '',
            'price' => (float)($product['price'] ?? 0),
            'seo_title' => $product['seo_title'] ?? '',
            'seo_description' => $product['seo_description'] ?? '',
            'seo_keywords' => $product['seo_keywords'] ?? '',
            'image_filename' => $product['image_filename'] ?? null
        ];
    }

    // Clear any output buffer
    ob_clean();

    // Return success response
    echo json_encode([
        'success' => true,
        'products' => $processed_products,
        'count' => count($processed_products)
    ]);

} catch (Exception $e) {
    // Clear any output buffer
    ob_clean();

    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'products' => []
    ]);
}
?>
